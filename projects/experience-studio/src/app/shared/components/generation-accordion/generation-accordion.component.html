@if (result) {
  <div class="generation-accordion" [ngClass]="[theme, getResultClass(), isCompactMode() ? 'compact-mode' : '']" #accordionContainer>
    <div class="accordion-header" (click)="toggleAccordion()" [attr.aria-expanded]="isExpanded()">
      <div class="header-content">
        <div class="version-title" #versionTitle>{{ getVersionTitle() }}</div>
        @if (result.type === 'success' && result.files) {
          <div class="file-count">({{ getFileCountText() }})</div>
        }
        @if (result.timestamp) {
          <div class="timestamp" #timestamp>{{ getFormattedTimestamp() }}</div>
        }
      </div>
      <div class="expand-icon" [class.expanded]="isExpanded()">
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round">
          <polyline points="6 9 12 15 18 9"></polyline>
        </svg>
      </div>
    </div>

    <div class="accordion-content" [class.expanded]="isExpanded()">
      <div class="content-inner">
        @if (result.type === 'success' && result.files) {
          <!-- Success content: List of generated files -->
          <div class="success-content">
            <div class="files-list">
              @for (file of result.files; track file) {
                <div class="file-item" [class.clickable]="result.isLatest" (click)="onFileClick(file)">
                  <span class="file-name">{{ file }}</span>
                </div>
              }
            </div>
          </div>
        } @else if (result.type === 'error') {
          <!-- Error content: Error message details -->
          <div class="error-content">
            @if (result.errorMessage) {
              <div class="error-message">
                <div class="error-label">Error Details:</div>
                <div class="error-text">{{ result.errorMessage }}</div>
              </div>
            }
            <div class="error-actions">
              <div class="retry-suggestion">
                Try generating again or check your request for any issues.
              </div>
              <!-- ENHANCED: Retry button for code regeneration errors -->
              <div class="retry-button-container">
                <button
                  mat-raised-button
                  color="primary"
                  class="retry-button"
                  [disabled]="isRetrying()"
                  (click)="onRetryRegeneration(result)"
                  type="button">
                  @if (isRetrying()) {
                    <ng-container>
                      <mat-icon class="retry-spinner">refresh</mat-icon>
                      Retrying...
                    </ng-container>
                  } @else {
                    <ng-container>
                      <mat-icon>refresh</mat-icon>
                      Retry Generation
                    </ng-container>
                  }
                </button>
              </div>
            </div>
          </div>
        }
      </div>
    </div>
  </div>
}
