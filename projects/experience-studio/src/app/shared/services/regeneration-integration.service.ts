import { Injectable, inject, DestroyRef } from '@angular/core';
import { BehaviorSubject, Observable, Subject, Subscription } from 'rxjs';
import { takeUntilDestroyed } from '@angular/core/rxjs-interop';
import { filter, tap } from 'rxjs/operators';
import { createLogger } from '../utils/logger';
import { SequentialRegenerationService } from './sequential-regeneration.service';
import { CodeRegenerationUIService, CodeRegenerationProgress } from './code-regeneration-ui.service';
import { StepperStateService } from './stepper-state.service';
import { FileModel } from '../components/code-viewer/code-viewer.component';

/**
 * Regeneration Integration Service
 *
 * This service coordinates the perfect integration between all regeneration-related components:
 * - Sequential Regeneration Service (handles SSE events and file extraction)
 * - Code Regeneration UI Service (handles UI state management)
 * - Code Window Component (handles file updates and accordion creation)
 * - Chat Window Component (handles loading indicators and user interaction)
 * - Code Viewer Component (handles file override and display)
 * - Generation Accordion Component (handles version tracking)
 * - Vertical Stepper Component (isolated during regeneration)
 *
 * Key Features:
 * - Processes "code-regen" SSE events with progress: "CODE_GENERATION", status: "COMPLETED"
 * - Extracts files from metadata exactly like PAGES_GENERATED step
 * - Overrides previous codes and file models (complete replacement)
 * - Creates new version accordions for each regeneration
 * - Manages UI state transitions (loading → code tab → preview tab)
 * - Isolates stepper during regeneration to prevent interference
 */
@Injectable({
  providedIn: 'root'
})
export class RegenerationIntegrationService {
  private readonly destroyRef = inject(DestroyRef);
  private readonly logger = createLogger('RegenerationIntegrationService');

  // Core services
  private readonly sequentialRegenerationService = inject(SequentialRegenerationService);
  private readonly codeRegenerationUIService = inject(CodeRegenerationUIService);
  private readonly stepperStateService = inject(StepperStateService);

  // State management
  private readonly regenerationActiveSubject = new BehaviorSubject<boolean>(false);
  private readonly currentVersionSubject = new BehaviorSubject<number>(1);
  private readonly latestFilesSubject = new BehaviorSubject<FileModel[]>([]);

  // ENHANCED: Current project and job ID tracking for retry functionality
  private currentProjectId: string | null = null;
  private currentJobId: string | null = null;

  // Event streams
  private readonly fileUpdateSubject = new Subject<{
    files: FileModel[];
    version: number;
    replaceAll: boolean;
  }>();
  private readonly accordionCreateSubject = new Subject<{
    files: string[];
    version: number;
    projectName: string;
    timestamp: Date;
    type?: 'success' | 'error'; // ENHANCED: Support error accordions
    errorMessage?: string; // ENHANCED: Error message for error accordions
    projectId?: string; // ENHANCED: Project ID for retry functionality
    jobId?: string; // ENHANCED: Job ID for retry functionality
  }>();
  private readonly uiStateUpdateSubject = new Subject<{
    phase: 'CODE_GENERATION' | 'BUILD' | 'DEPLOY';
    status: 'IN_PROGRESS' | 'COMPLETED';
    shouldSwitchTab?: 'code' | 'preview';
    shouldRefresh?: boolean;
    // ENHANCED: Progress description support
    description?: string;
    sessionId?: string;
    event?: string;
    isProgressDescription?: boolean;
  }>();

  // Public observables
  readonly regenerationActive$ = this.regenerationActiveSubject.asObservable();
  readonly currentVersion$ = this.currentVersionSubject.asObservable();
  readonly latestFiles$ = this.latestFilesSubject.asObservable();
  readonly fileUpdates$ = this.fileUpdateSubject.asObservable();
  readonly accordionCreate$ = this.accordionCreateSubject.asObservable();
  readonly uiStateUpdates$ = this.uiStateUpdateSubject.asObservable();

  // Subscription management
  private subscriptions = new Subscription();

  constructor() {
    this.logger.info('🔧 Regeneration Integration Service initialized');
    this.setupIntegrations();
  }

  /**
   * Setup integrations between all regeneration services
   * CRITICAL: Based on actual console SSE data structure
   */
  private setupIntegrations(): void {
    this.logger.info('🔗 Setting up regeneration service integrations based on console SSE data');

    // CRITICAL: Subscribe to sequential regeneration progress updates
    // This handles code-regen events with CODE_GENERATION COMPLETED containing files
    this.subscriptions.add(
      this.sequentialRegenerationService.progressUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          tap(update => this.logger.info('📨 Received progress update from sequential regeneration service:', {
            progress: update.progress,
            status: update.status,
            event: update.event,
            hasMetadata: !!update.metadata,
            deploymentCompleted: update.deploymentCompleted,
            willProcess: update.event === 'code-regen'
          })),
          filter(update => this.validateRegenerationEvent(update)), // Enhanced event validation
          tap(update => this.logger.info('✅ Processing code-regen progress update:', {
            progress: update.progress,
            status: update.status,
            hasMetadata: !!update.metadata,
            deploymentCompleted: update.deploymentCompleted
          }))
        )
        .subscribe(update => {
          this.processRegenerationProgress(update);
        })
    );

    // CRITICAL: Subscribe to sequential regeneration file updates
    // This handles files extracted from CODE_GENERATION COMPLETED events
    this.subscriptions.add(
      this.sequentialRegenerationService.fileUpdates$
        .pipe(
          takeUntilDestroyed(this.destroyRef),
          tap(files => this.logger.info('📁 Received file updates from sequential regeneration service:', {
            fileCount: files?.length || 0,
            files: files?.map(f => f.name || f.fileName || 'Unknown') || [],
            regenerationActive: this.regenerationActiveSubject.value,
            willProcess: this.regenerationActiveSubject.value
          })),
          filter(() => {
            const isActive = this.regenerationActiveSubject.value;
            if (!isActive) {
              this.logger.warn('🚫 Skipping file updates - regeneration not active');
            }
            return isActive;
          }), // Only during active regeneration
          tap(files => this.logger.info('✅ Processing regeneration file updates from SSE:', {
            fileCount: files?.length || 0,
            files: files?.map(f => f.name || f.fileName || 'Unknown') || []
          }))
        )
        .subscribe(files => {
          this.processFileUpdates(files);
        })
    );

    // DISABLED: Progress descriptions subscription (using accordions instead)
    // this.subscriptions.add(
    //   this.sequentialRegenerationService.progressDescriptions$
    //     .pipe(
    //       takeUntilDestroyed(this.destroyRef),
    //       tap(description => this.logger.info('📝 Progress description disabled - using accordions')),
    //       filter(() => this.regenerationActiveSubject.value)
    //     )
    //     .subscribe(description => {
    //       // DISABLED: Using accordions instead
    //     })
    // );

    // Setup cleanup
    this.destroyRef.onDestroy(() => {
      this.subscriptions.unsubscribe();
      this.logger.info('🧹 Regeneration Integration Service cleaned up');
    });
  }

  /**
   * Start regeneration process
   * ENHANCED: Now accepts project and job IDs for retry functionality
   */
  startRegeneration(projectId?: string, jobId?: string): void {
    this.logger.info('🚀 Starting regeneration process', { projectId, jobId });

    // ENHANCED: Store project and job IDs for retry functionality
    if (projectId && jobId) {
      this.currentProjectId = projectId;
      this.currentJobId = jobId;
    }

    // Mark regeneration as active
    this.regenerationActiveSubject.next(true);

    // Isolate stepper during regeneration
    this.stepperStateService.setRegenerationActive(true);

    this.logger.info('🔒 Regeneration started - stepper isolated', {
      regenerationActive: this.regenerationActiveSubject.value,
      timestamp: new Date().toISOString(),
      hasProjectId: !!this.currentProjectId,
      hasJobId: !!this.currentJobId
    });
  }

  /**
   * Complete regeneration process - ENHANCED for comprehensive cleanup
   * This method ensures all regeneration states are properly reset
   */
  completeRegeneration(): void {
    this.logger.info('🏁 Completing regeneration process with comprehensive cleanup');

    // Mark regeneration as inactive
    this.regenerationActiveSubject.next(false);

    // Re-enable stepper
    this.stepperStateService.setRegenerationActive(false);

    this.logger.info('🔓 Regeneration completed - stepper re-enabled', {
      regenerationActive: this.regenerationActiveSubject.value,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * ENHANCED: Validate regeneration event before processing
   * Ensures event type, progress, and status are valid for regeneration
   * ENHANCED: Now accepts FAILED status events for error handling
   */
  private validateRegenerationEvent(update: any): boolean {
    // Check if event type is correct
    if (update.event !== 'code-regen') {
      this.logger.debug('⏭️ Skipping non-code-regen event:', {
        received: update.event,
        expected: 'code-regen',
        reason: 'Event type mismatch for regeneration'
      });
      return false;
    }

    // Check if progress and status are valid (now including FAILED status)
    if (!update.progress || !update.status) {
      this.logger.warn('⚠️ Event missing progress or status:', {
        progress: update.progress,
        status: update.status,
        event: update.event,
        reason: 'Invalid event structure'
      });
      return false;
    }

    // ENHANCED: Accept FAILED status for error handling
    const validStatuses = ['IN_PROGRESS', 'COMPLETED', 'FAILED'];
    if (!validStatuses.includes(update.status)) {
      this.logger.warn('⚠️ Invalid status for regeneration event:', {
        status: update.status,
        validStatuses,
        event: update.event,
        reason: 'Status not supported for regeneration'
      });
      return false;
    }

    // Log successful validation
    this.logger.debug('✅ Event validation passed:', {
      event: update.event,
      progress: update.progress,
      status: update.status,
      hasMetadata: !!update.metadata,
      hasErrorMessage: !!update.errorMessage
    });

    return true;
  }



  /**
   * Process regeneration progress updates from SSE events
   * CRITICAL: Based on actual console SSE data structure
   * - CODE_GENERATION COMPLETED contains files in metadata
   * - DEPLOY COMPLETED triggers tab switch and iframe refresh
   * ENHANCED: Now handles FAILED status events for error accordion creation
   */
  private processRegenerationProgress(update: any): void {
    this.logger.info('🔄 Processing code-regen progress based on console SSE structure:', {
      progress: update.progress,
      status: update.status,
      event: update.event,
      hasMetadata: !!update.metadata,
      deploymentCompleted: update.deploymentCompleted,
      hasErrorMessage: !!update.errorMessage,
      hasProjectId: !!update.projectId,
      hasJobId: !!update.jobId
    });

    // ENHANCED: Store project and job IDs from progress update for retry functionality
    if (update.projectId && update.jobId) {
      this.currentProjectId = update.projectId;
      this.currentJobId = update.jobId;
      this.logger.info('🆔 Updated project/job IDs from progress update:', {
        projectId: update.projectId,
        jobId: update.jobId
      });
    }

    // ENHANCED: Handle FAILED events for error accordion creation
    if (update.status === 'FAILED') {
      this.logger.error('❌ Regeneration FAILED - creating error accordion:', {
        progress: update.progress,
        errorMessage: update.errorMessage?.substring(0, 100) + '...',
        hasLog: !!update.log,
        projectId: update.projectId,
        jobId: update.jobId
      });

      // Create error accordion with extracted error message
      this.createRegenerationErrorAccordion(update.errorMessage || 'Code regeneration failed. Please try again.');

      // Complete regeneration process (failed state)
      this.completeRegeneration();
      return;
    }

    // CRITICAL: Handle CODE_GENERATION COMPLETED with files
    if (update.progress === 'CODE_GENERATION' && update.status === 'COMPLETED') {
      this.logger.info('📁 CODE_GENERATION COMPLETED - processing files from metadata');

      // Extract files from metadata exactly like console SSE structure
      if (update.metadata && Array.isArray(update.metadata)) {
        const filesMetadata = update.metadata.find((item: any) => item.type === 'files');
        if (filesMetadata && filesMetadata.data && Array.isArray(filesMetadata.data)) {
          this.logger.info('✅ Found files in CODE_GENERATION COMPLETED metadata:', {
            fileCount: filesMetadata.data.length,
            files: filesMetadata.data.map((f: any) => f.fileName || 'Unknown')
          });

          // Process files directly (they will be handled by file updates subscription)
        }
      }
    }

    // Emit UI state update based on console SSE flow (skip for FAILED events)
    if (update.status !== 'FAILED') {
      this.uiStateUpdateSubject.next({
        phase: update.progress,
        status: update.status,
        shouldSwitchTab: this.determineSwitchTab(update.progress, update.status),
        shouldRefresh: update.progress === 'DEPLOY' && update.status === 'COMPLETED'
      });
    }

    // Handle completion based on console SSE structure
    if (update.deploymentCompleted || (update.progress === 'DEPLOY' && update.status === 'COMPLETED')) {
      this.logger.info('🎉 Regeneration deployment completed');

      // ENHANCED: Create accordion for completed regeneration
      this.createRegenerationAccordionOnDeployment();

      this.completeRegeneration();
    }
  }

  /**
   * Process file updates from regeneration
   * CRITICAL: Based on actual console SSE data structure
   * - Files come from CODE_GENERATION COMPLETED events
   * - Complete replacement for regeneration (not append)
   */
  private processFileUpdates(files: FileModel[]): void {
    if (!files || files.length === 0) {
      this.logger.warn('⚠️ No files received from CODE_GENERATION COMPLETED');
      return;
    }

    this.logger.info('📁 Processing file updates from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      source: 'code-regen SSE event'
    });

    // Increment version
    const newVersion = this.currentVersionSubject.value + 1;
    this.currentVersionSubject.next(newVersion);

    // Update latest files
    this.latestFilesSubject.next(files);

    // CRITICAL: Emit file update event with complete replacement flag for regeneration
    this.fileUpdateSubject.next({
      files,
      version: newVersion,
      replaceAll: true // Critical: Complete replacement for regeneration (not append)
    });

    // CRITICAL: Create accordion for regeneration with proper version tracking
    this.createRegenerationAccordion(files, newVersion);

    this.logger.info('✅ File updates processed for regeneration from SSE:', {
      version: newVersion,
      fileCount: files.length,
      replaceAll: true,
      source: 'CODE_GENERATION COMPLETED'
    });
  }

  /**
   * Handle code viewer updates
   */
  private handleCodeViewerUpdate(files: any[]): void {
    this.logger.info('🖥️ Handling code viewer update:', { fileCount: files?.length || 0 });

    // Convert to FileModel format if needed
    const fileModels = this.convertToFileModels(files);
    this.processFileUpdates(fileModels);
  }

  /**
   * Handle accordion updates
   */
  private handleAccordionUpdate(accordionData: any): void {
    this.logger.info('📋 Handling accordion update:', accordionData);

    if (accordionData.files && accordionData.files.length > 0) {
      // Extract file names
      const fileNames = accordionData.files.map((file: any) =>
        file.fileName || file.name || file.path || 'Unknown file'
      );

      // Emit accordion creation event
      this.accordionCreateSubject.next({
        files: fileNames,
        version: accordionData.version,
        projectName: 'Regenerated Project', // Could be made configurable
        timestamp: new Date()
      });
    }
  }

  /**
   * Convert various file formats to FileModel
   */
  private convertToFileModels(files: any[]): FileModel[] {
    if (!files || !Array.isArray(files)) {
      return [];
    }

    return files.map(file => ({
      name: file.fileName || file.name || file.path || 'Unknown file',
      fileName: file.fileName || file.name || file.path,
      type: 'file' as const,
      content: file.content || file.code || file.data || '',
    }));
  }

  /**
   * Create regeneration accordion with proper version tracking
   * CRITICAL: Based on actual console SSE data structure
   */
  private createRegenerationAccordion(files: FileModel[], version: number): void {
    this.logger.info('📋 Creating regeneration accordion from CODE_GENERATION COMPLETED:', {
      fileCount: files.length,
      version: version,
      files: files.map(f => f.name || f.fileName || 'Unknown')
    });

    // Create accordion data exactly like console SSE structure
    const accordionInfo = {
      files: files.map(f => f.name || f.fileName || 'Unknown'),
      version: version,
      projectName: 'Regenerated Project',
      timestamp: new Date()
    };

    // Emit accordion creation event
    this.accordionCreateSubject.next(accordionInfo);

    this.logger.info('✅ Regeneration accordion created:', accordionInfo);
  }

  /**
   * ENHANCED: Create error accordion for FAILED regeneration events
   */
  private createRegenerationErrorAccordion(errorMessage: string): void {
    this.logger.info('📋 Creating regeneration error accordion:', {
      errorMessage: errorMessage.substring(0, 100) + '...',
      version: this.currentVersionSubject.value,
      hasProjectId: !!this.currentProjectId,
      hasJobId: !!this.currentJobId
    });

    // Create error accordion data with proper structure including project/job IDs
    const errorAccordionInfo = {
      files: [], // No files for error accordion
      version: this.currentVersionSubject.value,
      projectName: 'Regeneration Failed',
      timestamp: new Date(),
      type: 'error' as const,
      errorMessage: errorMessage,
      // ENHANCED: Include project and job IDs for retry functionality (convert null to undefined)
      projectId: this.currentProjectId || undefined,
      jobId: this.currentJobId || undefined
    };

    // Emit error accordion creation event
    this.accordionCreateSubject.next(errorAccordionInfo);

    this.logger.info('✅ Regeneration error accordion created:', {
      version: errorAccordionInfo.version,
      hasErrorMessage: !!errorAccordionInfo.errorMessage,
      hasProjectId: !!errorAccordionInfo.projectId,
      hasJobId: !!errorAccordionInfo.jobId
    });
  }

  /**
   * ENHANCED: Create accordion when deployment completes
   */
  private createRegenerationAccordionOnDeployment(): void {
    const latestFiles = this.latestFilesSubject.value;
    const currentVersion = this.currentVersionSubject.value;

    this.logger.info('📋 Attempting to create accordion for deployment completion:', {
      hasFiles: !!latestFiles,
      fileCount: latestFiles?.length || 0,
      version: currentVersion,
      files: latestFiles?.map(f => f.fileName || f.name || 'Unknown') || []
    });

    if (latestFiles && latestFiles.length > 0) {
      this.logger.info('✅ Creating accordion for deployment completion with files');

      // Create accordion with latest files
      this.createRegenerationAccordion(latestFiles, currentVersion);
    } else {
      this.logger.warn('⚠️ No files available for accordion creation on deployment - checking alternative sources');

      // Try to get files from the last progress update
      this.tryCreateAccordionFromLastUpdate();
    }
  }

  /**
   * ENHANCED: Try to create accordion from last update if no files in latestFiles
   */
  private tryCreateAccordionFromLastUpdate(): void {
    // For now, create a simple accordion indicating regeneration completed
    const accordionInfo = {
      files: ['Regeneration completed successfully'],
      version: this.currentVersionSubject.value,
      projectName: 'Regenerated Project',
      timestamp: new Date()
    };

    this.logger.info('📋 Creating fallback accordion for regeneration completion');
    this.accordionCreateSubject.next(accordionInfo);
  }

  /**
   * DISABLED: Process progress description (using accordions instead)
   */
  private processProgressDescription(description: any): void {
    this.logger.info('📝 Progress description processing disabled - using accordions instead');
  }

  // DISABLED: Mapping methods (not needed for accordion-based approach)

  /**
   * Determine which tab to switch to based on progress and status
   */
  private determineSwitchTab(progress: string, status: string): 'code' | 'preview' | undefined {
    if (progress === 'CODE_GENERATION' && status === 'COMPLETED') {
      return 'code'; // Switch to code tab to show new files
    }
    if (progress === 'DEPLOY' && status === 'COMPLETED') {
      return 'preview'; // Switch to preview tab to show deployed app
    }
    return undefined;
  }

  /**
   * Get current regeneration state
   */
  isRegenerationActive(): boolean {
    return this.regenerationActiveSubject.value;
  }

  /**
   * Get current version
   */
  getCurrentVersion(): number {
    return this.currentVersionSubject.value;
  }

  /**
   * Get latest files
   */
  getLatestFiles(): FileModel[] {
    return this.latestFilesSubject.value;
  }
}
