import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, BehaviorSubject, of } from 'rxjs';
import { map, tap, switchMap, catchError, timeout } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { cacheHelpers } from '../interceptors/cache.interceptor';
import { createLogger } from '../utils/logger';
import { ToastService } from './toast.service';

// Interfaces for code generation intro
export interface CodeGenerationIntroRequest {
  code: CodeIntroItem[];
  user_request: string;
  message_type?: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress';
  context?: {
    stage?: string;
    progress?: string;
    error_details?: string;
    file_count?: number;
    preserved_edits?: number;
    conflicts?: number;
  };
}

export interface CodeIntroItem {
  fileName: string;
  content: string;
}

export interface CodeGenerationIntroResponse {
  message?: string;
  text?: string;
  content?: string;
}

export type CodeGenerationIntroResponseType = string | CodeGenerationIntroResponse;

export interface CodeIntroMessageState {
  isLoading: boolean;
  text: string;
  isTyping: boolean;
  hasError: boolean;
  shouldReplaceText: boolean;
  targetMessageId?: string;
  introAPICompleted: boolean;
  mainAPIInProgress: boolean;
  showLoadingIndicator: boolean;
  loadingPhase: 'intro' | 'main' | 'completed';
  messageType?: 'generation' | 'regeneration';
  sessionId?: string; // Session ID for regeneration isolation
}

export interface CodeParallelAPIResult {
  introText: string;
  mainAPIResult: any;
  introSuccess: boolean;
  mainAPISuccess: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class CodeGenerationIntroService {
  private apiUrl = environment.apiUrl;
  private introEndpoint = '/wireframe-generation/intro';
  private logger = createLogger('CodeGenerationIntroService');

  // ENHANCED: Session-based state management for intro messages
  // Each regeneration gets its own isolated intro message state
  private sessionStates = new Map<string, BehaviorSubject<CodeIntroMessageState>>();

  // Track which session is the "primary" one that should update global state
  private primarySessionId: string | null = null;

  // Global state for backward compatibility (used when no session ID provided)
  private introMessageStateSubject = new BehaviorSubject<CodeIntroMessageState>({
    isLoading: false,
    text: '',
    isTyping: false,
    hasError: false,
    shouldReplaceText: false,
    introAPICompleted: false,
    mainAPIInProgress: false,
    showLoadingIndicator: false,
    loadingPhase: 'intro',
    messageType: 'generation'
  });

  // ENHANCED: Session-based typewriter timeouts to prevent cross-session interference
  private sessionTypewriterTimeouts = new Map<string, NodeJS.Timeout[]>();

  // Public observables
  public introMessageState$ = this.introMessageStateSubject.asObservable();

  // Typewriter effect configuration
  private typingSpeed = 15; // milliseconds per character
  private typewriterTimeouts: NodeJS.Timeout[] = [];

  constructor(
    private http: HttpClient,
    private toastService: ToastService
  ) {}

  /**
   * ENHANCED: Get or create session-based intro message state
   * Each regeneration session gets its own isolated state
   */
  private getSessionState(sessionId: string): BehaviorSubject<CodeIntroMessageState> {
    if (!this.sessionStates.has(sessionId)) {
      this.logger.info('🆕 Creating new session state for intro messages:', sessionId);
      const sessionState = new BehaviorSubject<CodeIntroMessageState>({
        isLoading: false,
        text: '',
        isTyping: false,
        hasError: false,
        shouldReplaceText: false,
        introAPICompleted: false,
        mainAPIInProgress: false,
        showLoadingIndicator: false,
        loadingPhase: 'intro',
        messageType: 'generation'
      });
      this.sessionStates.set(sessionId, sessionState);
      this.sessionTypewriterTimeouts.set(sessionId, []);
    }
    return this.sessionStates.get(sessionId)!;
  }

  /**
   * ENHANCED: Get session-specific intro message state observable
   * Allows components to subscribe to specific session states
   */
  public getSessionIntroMessageState$(sessionId: string): Observable<CodeIntroMessageState> {
    return this.getSessionState(sessionId).asObservable();
  }

  /**
   * ENHANCED: Get current session ID if available
   * Returns the most recently active session ID
   */
  public getCurrentSessionId(): string | null {
    // Return the most recently created session ID
    const sessionIds = Array.from(this.sessionStates.keys());
    return sessionIds.length > 0 ? sessionIds[sessionIds.length - 1] : null;
  }

  /**
   * ENHANCED: Get session-based typewriter timeouts
   */
  private getSessionTimeouts(sessionId: string): NodeJS.Timeout[] {
    if (!this.sessionTypewriterTimeouts.has(sessionId)) {
      this.sessionTypewriterTimeouts.set(sessionId, []);
    }
    return this.sessionTypewriterTimeouts.get(sessionId)!;
  }

  /**
   * ENHANCED: Clear session-based typewriter timeouts
   */
  private clearSessionTypewriterTimeouts(sessionId: string): void {
    const timeouts = this.getSessionTimeouts(sessionId);
    timeouts.forEach(timeout => clearTimeout(timeout));
    this.sessionTypewriterTimeouts.set(sessionId, []);
    this.logger.debug('🧹 Cleared session typewriter timeouts:', sessionId);
  }

  /**
   * ENHANCED: Clean up session resources
   */
  public cleanupSession(sessionId: string): void {
    this.logger.info('🧹 Cleaning up session resources:', sessionId);

    // Clear typewriter timeouts
    this.clearSessionTypewriterTimeouts(sessionId);

    // Complete and remove session state
    const sessionState = this.sessionStates.get(sessionId);
    if (sessionState) {
      sessionState.complete();
      this.sessionStates.delete(sessionId);
    }

    // Remove timeout tracking
    this.sessionTypewriterTimeouts.delete(sessionId);

    // Reset primary session if this was the primary one
    if (this.primarySessionId === sessionId) {
      this.primarySessionId = null;
      this.logger.info('🎯 Reset primary session ID after cleanup:', sessionId);
    }

    this.logger.info('✅ Session cleanup completed:', sessionId);
  }

  /**
   * Execute parallel API calls for code regeneration with intro message
   * @param userRequest - The user's prompt
   * @param codeFiles - Array of current code files
   * @param mainAPICall - Observable for the main regeneration API
   * @param targetMessageId - ID of the chat message to replace text in
   * @returns Observable<CodeParallelAPIResult>
   */
  executeParallelRegeneration(
    userRequest: string,
    codeFiles: any[],
    mainAPICall: Observable<any>,
    targetMessageId?: string
  ): Observable<CodeParallelAPIResult> {
    this.logger.info('🔄 Starting parallel API calls for code regeneration with intro message');
    this.logger.info('Code files count:', codeFiles.length);

    // Generate unique session ID for this regeneration to prevent cross-regeneration interference
    const sessionId = `regen_session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    this.logger.info('🆔 Generated session ID for regeneration:', sessionId);

    // Initialize loading state for regeneration intro phase
    this.updateIntroState({
      isLoading: true,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: !!targetMessageId,
      targetMessageId,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'intro',
      messageType: 'regeneration',
      sessionId // Add session ID to state
    });

    // Convert code files to intro code items
    const codeItems: CodeIntroItem[] = this.convertCodeFilesToIntroItems(codeFiles);

    // Create intro API call with code file data
    const introAPICall = this.generateCodeIntroMessage(userRequest, codeItems);

    // Execute intro API first for regeneration, then handle main API separately
    return introAPICall.pipe(
      tap(introText => {
        this.logger.info('🎭 Code regeneration intro API completed successfully');
        this.logger.info('🎭 Code regeneration intro text received:', {
          length: introText?.length || 0,
          preview: introText?.substring(0, 100) + (introText?.length > 100 ? '...' : ''),
          type: typeof introText
        });

        // Validate and sanitize intro text
        const validatedIntroText = this.validateIntroText(introText);

        // Update state to show intro API completed, main regeneration API still in progress
        this.updateIntroState({
          isLoading: false,
          text: validatedIntroText,
          isTyping: false,
          hasError: false,
          shouldReplaceText: !!targetMessageId,
          targetMessageId,
          introAPICompleted: true,
          mainAPIInProgress: true,
          showLoadingIndicator: true,
          loadingPhase: 'main'
        });

        // Start text replacement with typewriter effect and loading indicator
        if (targetMessageId) {
          this.startTextReplacementWithLoading(validatedIntroText, targetMessageId, sessionId);
        } else {
          this.startTypewriterEffectWithLoading(validatedIntroText);
        }
      }),
      switchMap(introText => {
        // Now execute main regeneration API and combine results
        return mainAPICall.pipe(
          map(mainAPIResult => ({
            introText,
            mainAPIResult,
            introSuccess: true,
            mainAPISuccess: true
          })),
          tap(result => {
            this.logger.info('🎉 Main code regeneration API completed successfully - keeping intro message visible');

            // CRITICAL FIX: Keep intro message visible and maintain loading state for SSE
            // Do NOT mark as completed yet - SSE events still need to process
            this.updateIntroState({
              isLoading: true, // Keep loading for SSE events
              text: this.validateIntroText(result.introText),
              isTyping: false,
              hasError: false,
              shouldReplaceText: !!targetMessageId,
              targetMessageId,
              introAPICompleted: true,
              mainAPIInProgress: false, // Main API done, but SSE still processing
              showLoadingIndicator: true, // Keep loading indicator for SSE
              loadingPhase: 'main' // Keep in main phase for SSE processing
            });

            this.logger.info('✅ Regeneration API completed - intro message kept visible, waiting for SSE completion');
          })
        );
      }),
      catchError(error => this.handleParallelAPIError(error))
    );
  }

  /**
   * Call the intro API to get contextual messaging for code regeneration
   * @param userRequest - The user's prompt/request
   * @param codeItems - Array of code items for regeneration context
   * @param messageType - Type of message to generate
   * @param context - Additional context for the message
   * @returns Observable<string> - The intro text response
   */
  generateCodeIntroMessage(
    userRequest: string,
    codeItems: CodeIntroItem[] = [],
    messageType: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress' = 'initial',
    context?: any
  ): Observable<string> {
    this.logger.info('🎭 Calling code generation intro API for contextual messaging');
    this.logger.info('🎭 User request:', userRequest);
    this.logger.info('🎭 Code items count:', codeItems.length);

    // Validate input parameters
    if (!userRequest || userRequest.trim().length === 0) {
      this.logger.warn('🎭 Empty user request provided, using fallback');
      return of('Preparing your code regeneration...');
    }

    // Build and validate the request
    const request: CodeGenerationIntroRequest = {
      code: codeItems,
      user_request: userRequest.trim(),
      message_type: messageType,
      context: context
    };

    // Log the request payload for debugging
    this.logger.info('🎭 Code intro API request payload:', {
      user_request: request.user_request,
      code_items_count: request.code.length,
      code_items_preview: request.code.slice(0, 2).map(item => ({
        fileName: item.fileName,
        contentLength: item.content.length
      }))
    });

    // Disable caching for intro requests
    const cacheContext = cacheHelpers.disableCache();

    this.logger.info('🎭 Code intro API endpoint:', this.apiUrl + this.introEndpoint);

    return this.http.post<CodeGenerationIntroResponseType>(this.apiUrl + this.introEndpoint, request, {
      context: cacheContext,
      headers: {
        'Content-Type': 'application/json'
      }
    }).pipe(
      map(response => {
        this.logger.info('🎭 Code intro API response received:', response);

        // Handle both string and object response formats
        let introText: string = '';

        if (typeof response === 'string') {
          // Direct string response (new format)
          introText = response.trim();
          this.logger.info('🎭 Received direct string response from code intro API');
        } else if (response && typeof response === 'object') {
          // Object response (legacy format)
          introText = response.message || response.text || response.content || '';
          this.logger.info('🎭 Received object response from code intro API, extracted text from fields');
        } else {
          this.logger.warn('🎭 Unexpected response format from code intro API:', typeof response);
        }

        // Validate extracted text
        if (!introText || introText.trim().length === 0) {
          this.logger.warn('🎭 Code intro API response contains no text content');
          return 'Preparing your code regeneration...';
        }

        // Clean and validate the intro text
        const cleanedText = this.sanitizeIntroText(introText);
        this.logger.info('🎭 Successfully extracted and cleaned code intro text:', {
          originalLength: introText.length,
          cleanedLength: cleanedText.length,
          preview: cleanedText.substring(0, 100) + (cleanedText.length > 100 ? '...' : '')
        });

        return cleanedText;
      }),
      catchError(error => {
        this.logger.error('🎭 Code intro API call failed:', error);

        // Log detailed error information for debugging
        this.logger.error('🎭 Error details:', {
          status: error.status,
          statusText: error.statusText,
          message: error.message,
          url: error.url
        });

        // Return fallback message instead of throwing error
        const fallbackMessage = this.getFallbackIntroMessage(error);
        this.logger.info('🎭 Using fallback code intro message:', fallbackMessage);

        return of(fallbackMessage);
      }),
      timeout(30000), // 30 second timeout for intro API
      catchError(timeoutError => {
        this.logger.error('🎭 Code intro API timeout after 30 seconds:', timeoutError);
        return of('Preparing your code regeneration...');
      })
    );
  }

  /**
   * Convert code files to intro code items
   * @param codeFiles - Array of code files
   * @returns Array of CodeIntroItem
   */
  private convertCodeFilesToIntroItems(codeFiles: any[]): CodeIntroItem[] {
    if (!codeFiles || !Array.isArray(codeFiles)) {
      this.logger.warn('🎭 Invalid code files provided for intro conversion');
      return [];
    }

    return codeFiles.map(file => ({
      fileName: file.path || file.fileName || file.name || 'Unknown file',
      content: file.code || file.content || ''
    })).filter(item => item.fileName && item.fileName !== 'Unknown file');
  }

  /**
   * Validate and sanitize intro text
   * @param text - Raw intro text
   * @returns Validated intro text
   */
  private validateIntroText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'Preparing your code regeneration...';
    }

    const cleanedText = this.sanitizeIntroText(text);
    return cleanedText.length > 0 ? cleanedText : 'Preparing your code regeneration...';
  }

  /**
   * Sanitize and clean intro text from API response
   * @param text - Raw intro text from API
   * @returns Cleaned and validated intro text
   */
  private sanitizeIntroText(text: string): string {
    if (!text || typeof text !== 'string') {
      return 'Preparing your code regeneration...';
    }

    // Remove any potential HTML tags or unwanted characters
    let cleanedText = text
      .trim()
      // Remove HTML tags if any
      .replace(/<[^>]*>/g, '')
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove any control characters
      .replace(/[\x00-\x1F\x7F]/g, '')
      // Trim again after cleaning
      .trim();

    // Validate length (reasonable limits for intro text)
    if (cleanedText.length === 0) {
      this.logger.warn('🎭 Code intro text is empty after sanitization');
      return 'Preparing your code regeneration...';
    }

    if (cleanedText.length > 500) {
      this.logger.warn('🎭 Code intro text is very long, truncating');
      cleanedText = cleanedText.substring(0, 500) + '...';
    }

    return cleanedText;
  }

  /**
   * Get appropriate fallback intro message based on error type
   * @param error - The error that occurred
   * @returns Appropriate fallback message
   */
  private getFallbackIntroMessage(error: any): string {
    // Provide different fallback messages based on error type
    if (error.status === 0) {
      // Network error
      return 'Preparing your code regeneration...';
    } else if (error.status >= 500) {
      // Server error
      return 'Getting ready to regenerate your code...';
    } else if (error.status === 404) {
      // Not found
      return 'Setting up code regeneration...';
    } else if (error.status >= 400 && error.status < 500) {
      // Client error
      return 'Initializing code regeneration...';
    } else {
      // Unknown error
      return 'Preparing your code regeneration...';
    }
  }

  /**
   * Update intro message state
   * @param newState - Partial state to update
   */
  private updateIntroState(newState: Partial<CodeIntroMessageState>): void {
    const currentState = this.introMessageStateSubject.getValue();
    const updatedState = { ...currentState, ...newState };
    this.introMessageStateSubject.next(updatedState);

    this.logger.debug('🎭 Code intro state updated:', {
      isLoading: updatedState.isLoading,
      isTyping: updatedState.isTyping,
      hasError: updatedState.hasError,
      loadingPhase: updatedState.loadingPhase,
      textLength: updatedState.text.length
    });
  }

  /**
   * Start typewriter effect for intro text
   * @param text - The text to type
   */
  public startTypewriterEffect(text: string): void {
    this.logger.info('⌨️ Starting typewriter effect for code intro text');

    // Clear any existing timeouts
    this.clearTypewriterTimeouts();

    // Update state to show typing
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro'
    });

    // Start typing character by character
    this.typeCharacter(text, 0);
  }

  /**
   * Start typewriter effect with loading indicator
   * @param text - The text to type
   */
  public startTypewriterEffectWithLoading(text: string): void {
    this.logger.info('⌨️ Starting typewriter effect with loading indicator for code intro text');

    // Clear any existing timeouts
    this.clearTypewriterTimeouts();

    // Update state to show typing with loading indicator (preserve loading state)
    const currentState = this.introMessageStateSubject.value;
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Start typing character by character with loading state preserved
    this.typeCharacterWithLoading(text, 0);
  }

  /**
   * Type a single character with typewriter effect
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   */
  private typeCharacter(fullText: string, charIndex: number): void {
    if (charIndex >= fullText.length) {
      // Typing complete
      this.updateIntroState({
        isLoading: false,
        text: fullText,
        isTyping: false,
        hasError: false,
        shouldReplaceText: false,
        targetMessageId: undefined,
        introAPICompleted: true,
        mainAPIInProgress: false,
        showLoadingIndicator: false,
        loadingPhase: 'completed'
      });
      return;
    }

    // Update text with current progress
    const currentText = fullText.substring(0, charIndex + 1);
    const currentState = this.introMessageStateSubject.getValue();
    this.updateIntroState({
      isLoading: false,
      text: currentText,
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: currentState.introAPICompleted,
      mainAPIInProgress: currentState.mainAPIInProgress,
      showLoadingIndicator: currentState.showLoadingIndicator,
      loadingPhase: currentState.loadingPhase
    });

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeCharacter(fullText, charIndex + 1);
    }, this.typingSpeed);

    this.typewriterTimeouts.push(timeout);
  }

  /**
   * Type a single character with typewriter effect and loading indicator
   * @param fullText - The complete text to type
   * @param charIndex - Current character index
   */
  private typeCharacterWithLoading(fullText: string, charIndex: number): void {
    if (charIndex >= fullText.length) {
      // Typing complete but keep loading state for main API
      const currentState = this.introMessageStateSubject.getValue();
      this.updateIntroState({
        isLoading: false,
        text: fullText,
        isTyping: false,
        hasError: false,
        shouldReplaceText: currentState.shouldReplaceText,
        targetMessageId: currentState.targetMessageId,
        introAPICompleted: true,
        mainAPIInProgress: true,
        showLoadingIndicator: true,
        loadingPhase: 'main'
      });
      return;
    }

    // Update text with current progress - preserve loading state
    const currentText = fullText.substring(0, charIndex + 1);
    const currentState = this.introMessageStateSubject.getValue();
    this.updateIntroState({
      isLoading: false,
      text: currentText,
      isTyping: true,
      hasError: false,
      shouldReplaceText: currentState.shouldReplaceText,
      targetMessageId: currentState.targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeCharacterWithLoading(fullText, charIndex + 1);
    }, this.typingSpeed);

    this.typewriterTimeouts.push(timeout);
  }

  /**
   * Start text replacement with typewriter effect and loading indicator for a specific message
   * ENHANCED: Now supports session-based isolation to prevent cross-regeneration interference
   */
  public startTextReplacementWithLoading(text: string, targetMessageId: string, sessionId?: string): void {
    this.logger.info('🔄 Starting text replacement with typewriter effect and loading indicator:', {
      text: text.substring(0, 100),
      targetMessageId,
      sessionId: sessionId || 'global'
    });

    if (sessionId) {
      // ENHANCED: Session-based text replacement (isolated per regeneration)
      this.startSessionBasedTextReplacement(text, targetMessageId, sessionId);
    } else {
      // Legacy: Global text replacement (backward compatibility)
      this.clearTypewriterTimeouts();
      this.updateIntroState({
        isLoading: false,
        text: '',
        isTyping: true,
        hasError: false,
        shouldReplaceText: true,
        targetMessageId,
        introAPICompleted: true,
        mainAPIInProgress: true,
        showLoadingIndicator: true,
        loadingPhase: 'main'
      });
      this.startTypewriterEffectWithLoading(text);
    }
  }

  /**
   * ENHANCED: Session-based text replacement with typewriter effect
   * Each regeneration session gets isolated typewriter state
   * FIXED: Only updates global state for the primary session to prevent overwriting
   */
  private startSessionBasedTextReplacement(text: string, targetMessageId: string, sessionId: string): void {
    this.logger.info('🔄 Starting session-based text replacement:', { sessionId, targetMessageId });

    // Clear any existing typewriter effects for this session
    this.clearSessionTypewriterTimeouts(sessionId);

    // Set this as the primary session if none exists
    if (!this.primarySessionId) {
      this.primarySessionId = sessionId;
      this.logger.info('🎯 Set primary session for global state updates:', sessionId);
    }

    // Get session-specific state
    const sessionState = this.getSessionState(sessionId);

    // Set initial state for text replacement with loading (session-isolated)
    const initialState = {
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: true,
      targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main' as const,
      messageType: 'generation' as const
    };

    // Update session-specific state
    sessionState.next(initialState);

    // Only update global state if this is the primary session
    if (this.primarySessionId === sessionId) {
      this.updateIntroState(initialState);
      this.logger.info('✅ Updated both session and global states (primary session):', { sessionId, targetMessageId });
    } else {
      this.logger.info('✅ Updated session state only (non-primary session):', { sessionId, targetMessageId });
    }

    // Start session-based typewriter effect
    this.startSessionBasedTypewriterEffect(text, sessionId);
  }

  /**
   * ENHANCED: Session-based typewriter effect with loading indicator
   * Each session gets its own isolated typewriter animation
   * FIXED: Only updates global state for the primary session to prevent overwriting
   */
  private startSessionBasedTypewriterEffect(text: string, sessionId: string): void {
    this.logger.info('⌨️ Starting session-based typewriter effect:', { sessionId, textLength: text.length });

    // Clear any existing timeouts for this session
    this.clearSessionTypewriterTimeouts(sessionId);

    // Get session state
    const sessionState = this.getSessionState(sessionId);

    // Update state to show typing with loading indicator
    const typingState = {
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: true,
      targetMessageId: sessionState.value.targetMessageId,
      introAPICompleted: true,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main' as const,
      messageType: 'generation' as const
    };

    // Update session-specific state
    sessionState.next(typingState);

    // Only update global state if this is the primary session
    if (this.primarySessionId === sessionId) {
      this.updateIntroState(typingState);
      this.logger.info('✅ Updated both session and global states for typewriter start (primary session):', { sessionId });
    } else {
      this.logger.info('✅ Updated session state only for typewriter start (non-primary session):', { sessionId });
    }

    // Start typing character by character
    this.typeSessionBasedCharacter(text, 0, sessionId);
  }

  /**
   * ENHANCED: Session-based character typing
   * Types one character at a time for the specified session
   */
  private typeSessionBasedCharacter(fullText: string, charIndex: number, sessionId: string): void {
    // Check if session still exists (might have been cleaned up)
    if (!this.sessionStates.has(sessionId)) {
      this.logger.warn('⚠️ Session no longer exists, stopping typewriter:', sessionId);
      return;
    }

    // Check if we've finished typing
    if (charIndex >= fullText.length) {
      this.completeSessionBasedTypewriter(sessionId);
      return;
    }

    const sessionState = this.getSessionState(sessionId);
    const currentState = sessionState.value;

    // Update text with current progress
    const currentText = fullText.substring(0, charIndex + 1);
    const updatedState = {
      ...currentState,
      text: currentText,
      isTyping: true
    };

    // Update session-specific state
    sessionState.next(updatedState);

    // Only update global state if this is the primary session
    if (this.primarySessionId === sessionId) {
      this.updateIntroState(updatedState);
    }

    // Schedule next character
    const timeout = setTimeout(() => {
      this.typeSessionBasedCharacter(fullText, charIndex + 1, sessionId);
    }, this.typingSpeed);

    // Store timeout for this session
    const sessionTimeouts = this.getSessionTimeouts(sessionId);
    sessionTimeouts.push(timeout);
  }

  /**
   * ENHANCED: Complete session-based typewriter effect
   * FIXED: Only updates global state for the primary session to prevent overwriting
   */
  private completeSessionBasedTypewriter(sessionId: string): void {
    this.logger.info('✅ Session-based typewriter effect completed:', sessionId);

    const sessionState = this.getSessionState(sessionId);
    const currentState = sessionState.value;

    // Mark typing as complete while preserving other state
    const completedState = {
      ...currentState,
      isTyping: false,
      introAPICompleted: true
    };

    // Update session-specific state
    sessionState.next(completedState);

    // Only update global state if this is the primary session
    if (this.primarySessionId === sessionId) {
      this.updateIntroState(completedState);
      this.logger.info('✅ Updated both session and global states for typewriter completion (primary session):', { sessionId });
    } else {
      this.logger.info('✅ Updated session state only for typewriter completion (non-primary session):', { sessionId });
    }

    // Clear timeouts for this session
    this.clearSessionTypewriterTimeouts(sessionId);
  }

  /**
   * Show fallback intro message when intro API fails
   */
  private showFallbackIntroMessage(): void {
    this.logger.info('🔄 Showing fallback code intro message');

    // Update state to show fallback with loading indicator (main API might still be running)
    this.updateIntroState({
      isLoading: false,
      text: '',
      isTyping: true,
      hasError: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: true,
      showLoadingIndicator: true,
      loadingPhase: 'main'
    });

    this.startTypewriterEffectWithLoading('Preparing your code regeneration...');

    // Show a subtle toast notification
    this.toastService.info('Regenerating your code...');
  }

  /**
   * Show error intro message when intro API fails
   */
  private showErrorIntroMessage(): void {
    this.logger.error('💥 Showing error code intro message');

    // Update state to show error
    this.updateIntroState({
      isLoading: false,
      text: 'Unable to prepare code regeneration context. Please try again.',
      isTyping: false,
      hasError: true,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'completed'
    });

    // Show error toast
    this.toastService.error('Failed to prepare regeneration context');
  }

  /**
   * Clear all typewriter timeouts
   */
  private clearTypewriterTimeouts(): void {
    this.typewriterTimeouts.forEach(timeout => clearTimeout(timeout));
    this.typewriterTimeouts = [];
  }

  /**
   * Complete text replacement and clear all loading states
   */
  public completeTextReplacement(): void {
    this.logger.info('✅ Completing code intro text replacement and clearing all loading states');

    const currentState = this.introMessageStateSubject.getValue();
    this.updateIntroState({
      ...currentState,
      isLoading: false,
      isTyping: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: true,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'completed'
    });
  }

  /**
   * Complete regeneration process after SSE events are processed
   * ENHANCEMENT: New method to complete intro after SSE completion
   */
  public completeRegenerationAfterSSE(): void {
    this.logger.info('✅ Completing regeneration intro after SSE events processed');

    const currentState = this.introMessageStateSubject.getValue();

    // Update state to show final completion while preserving intro text
    this.updateIntroState({
      ...currentState,
      isLoading: false,
      isTyping: false,
      shouldReplaceText: false,
      targetMessageId: undefined,
      introAPICompleted: true,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'completed'
    });

    this.logger.info('🎉 Regeneration process fully completed - intro message finalized');
  }

  /**
   * Reset the intro service state
   */
  public resetState(): void {
    this.logger.info('🔄 Resetting code intro service state');

    this.clearTypewriterTimeouts();

    this.introMessageStateSubject.next({
      isLoading: false,
      text: '',
      isTyping: false,
      hasError: false,
      shouldReplaceText: false,
      introAPICompleted: false,
      mainAPIInProgress: false,
      showLoadingIndicator: false,
      loadingPhase: 'intro',
      messageType: 'generation'
    });
  }

  /**
   * Generate progress message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param stage - Current stage (e.g., 'building', 'deploying', 'analyzing')
   * @returns Observable<string>
   */
  generateProgressMessage(userRequest: string, codeFiles: any[], stage: string): Observable<string> {
    this.logger.info('🔄 Generating progress message for stage:', stage);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'progress', { stage }).pipe(
      catchError(error => {
        this.logger.error('❌ Progress message generation failed:', error);
        return of(this.getFallbackMessage('progress', { stage }));
      })
    );
  }

  /**
   * Generate completion message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param results - Completion results (preserved edits, updated files, conflicts)
   * @returns Observable<string>
   */
  generateCompletionMessage(
    userRequest: string,
    codeFiles: any[],
    results: { preservedEdits: number; updatedFiles: number; conflicts: number }
  ): Observable<string> {
    this.logger.info('✅ Generating completion message with results:', results);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'completion', {
      preserved_edits: results.preservedEdits,
      file_count: results.updatedFiles,
      conflicts: results.conflicts
    }).pipe(
      catchError(error => {
        this.logger.error('❌ Completion message generation failed:', error);
        return of(this.getFallbackMessage('completion', {
          preserved_edits: results.preservedEdits,
          file_count: results.updatedFiles,
          conflicts: results.conflicts
        }));
      })
    );
  }

  /**
   * Generate error message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param errorDetails - Error details
   * @returns Observable<string>
   */
  generateErrorMessage(userRequest: string, codeFiles: any[], errorDetails: string): Observable<string> {
    this.logger.error('❌ Generating error message for:', errorDetails);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'error', { error_details: errorDetails }).pipe(
      catchError(error => {
        this.logger.error('❌ Error message generation failed:', error);
        return of(this.getFallbackMessage('error', { error_details: errorDetails }));
      })
    );
  }

  /**
   * Generate polling start message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @returns Observable<string>
   */
  generatePollingStartMessage(userRequest: string, codeFiles: any[]): Observable<string> {
    this.logger.info('🔄 Generating polling start message');

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'polling_start').pipe(
      catchError(error => {
        this.logger.error('❌ Polling start message generation failed:', error);
        return of(this.getFallbackMessage('polling_start'));
      })
    );
  }

  /**
   * Generate polling progress message for code regeneration
   * @param userRequest - The user's request
   * @param codeFiles - Array of code files
   * @param progress - Current progress description
   * @returns Observable<string>
   */
  generatePollingProgressMessage(userRequest: string, codeFiles: any[], progress: string): Observable<string> {
    this.logger.info('📊 Generating polling progress message for:', progress);

    const codeItems = this.convertCodeFilesToIntroItems(codeFiles);
    return this.generateCodeIntroMessage(userRequest, codeItems, 'polling_progress', { progress }).pipe(
      catchError(error => {
        this.logger.error('❌ Polling progress message generation failed:', error);
        return of(this.getFallbackMessage('polling_progress', { progress }));
      })
    );
  }

  /**
   * Get fallback message based on message type
   * @param messageType - Type of message
   * @param context - Additional context
   * @returns Fallback message string
   */
  getFallbackMessage(
    messageType: 'initial' | 'progress' | 'completion' | 'error' | 'polling_start' | 'polling_progress',
    context?: any
  ): string {
    // AUDIT CLEANUP: Removed hardcoded regeneration messages - now uses generic fallbacks
    switch (messageType) {
      case 'initial':
        return 'Processing your request...';
      case 'progress':
        return context?.stage ? `${context.stage}...` : 'Processing...';
      case 'completion':
        if (context?.preserved_edits !== undefined && context?.file_count !== undefined && context?.conflicts !== undefined) {
          return `Process completed! Preserved ${context.preserved_edits} user edits, updated ${context.file_count} files, ${context.conflicts} conflicts detected.`;
        }
        return 'Process completed successfully!';
      case 'error':
        return context?.error_details || 'Process failed. Please try again.';
      case 'polling_start':
        return 'Starting process...';
      case 'polling_progress':
        return context?.progress || 'Processing...';
      default:
        return 'Processing your request...';
    }
  }

  /**
   * Handle parallel API error scenarios
   * @param error - The error that occurred
   * @returns Observable with appropriate error handling
   */
  private handleParallelAPIError(error: any): Observable<CodeParallelAPIResult> {
    this.logger.error('💥 Code regeneration parallel API error:', error);

    // Check if error contains partial results
    const hasIntroText = error.introText && typeof error.introText === 'string';
    const hasMainAPIResult = error.mainAPIResult !== undefined && error.mainAPIResult !== null;

    if (hasIntroText && !hasMainAPIResult) {
      // Main API failed, intro succeeded
      this.logger.info('📝 Code intro API succeeded, main API failed');
      const validatedIntroText = this.validateIntroText(error.introText);
      this.startTypewriterEffect(validatedIntroText);

      return of({
        introText: validatedIntroText,
        mainAPIResult: null,
        introSuccess: true,
        mainAPISuccess: false
      });
    } else if (!hasIntroText && hasMainAPIResult) {
      // Intro API failed, main succeeded
      this.logger.info('🎯 Main API succeeded, code intro API failed');
      this.showFallbackIntroMessage();

      return of({
        introText: 'Preparing your code regeneration...',
        mainAPIResult: error.mainAPIResult,
        introSuccess: false,
        mainAPISuccess: true
      });
    } else {
      // Both failed or unknown error structure
      this.logger.error('💥 Both APIs failed or unknown error structure');
      this.logger.error('💥 Full error object:', error);

      // Show error state but don't re-throw to prevent breaking the main flow
      this.showErrorIntroMessage();

      return of({
        introText: 'Unable to prepare code regeneration context. Please try again.',
        mainAPIResult: null,
        introSuccess: false,
        mainAPISuccess: false
      });
    }
  }
}
